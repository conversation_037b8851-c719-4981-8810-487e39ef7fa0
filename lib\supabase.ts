import { createClient } from "@supabase/supabase-js";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const createClientComponent = () => {
  return createClientComponentClient();
};

export type Database = {
  public: {
    Tables: {
      village_info: {
        Row: {
          id: string;
          name: string;
          history: string;
          vision: string;
          mission: string;
          geography: string;
          demographics: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          history: string;
          vision: string;
          mission: string;
          geography: string;
          demographics?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          history?: string;
          vision?: string;
          mission?: string;
          geography?: string;
          demographics?: any;
          updated_at?: string;
        };
      };
      announcements: {
        Row: {
          id: string;
          title: string;
          content: string;
          is_important: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          content: string;
          is_important?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          content?: string;
          is_important?: boolean;
          updated_at?: string;
        };
      };
      umkm: {
        Row: {
          id: string;
          name: string;
          description: string;
          category: string;
          owner_name: string;
          contact_phone: string;
          contact_email: string | null;
          address: string;
          image_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description: string;
          category: string;
          owner_name: string;
          contact_phone: string;
          contact_email?: string | null;
          address: string;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string;
          category?: string;
          owner_name?: string;
          contact_phone?: string;
          contact_email?: string | null;
          address?: string;
          image_url?: string | null;
          updated_at?: string;
        };
      };
      contacts: {
        Row: {
          id: string;
          name: string;
          position: string;
          phone: string;
          email: string | null;
          office_hours: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          position: string;
          phone: string;
          email?: string | null;
          office_hours: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          position?: string;
          phone?: string;
          email?: string | null;
          office_hours?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
};

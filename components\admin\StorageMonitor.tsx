'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { Database, RefreshCw, HardDrive } from 'lucide-react';

interface StorageInfo {
  used: number;
  total: number;
  percentage: number;
}

export function StorageMonitor() {
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchStorageInfo();
  }, []);

  const fetchStorageInfo = async () => {
    setRefreshing(true);
    try {
      // Get storage usage info using Supabase API
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/storage/buckets`, {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        }
      });

      if (response.ok) {
        const buckets = await response.json();
        
        // Calculate total usage (this is a simplified example)
        // In a real implementation, you'd get actual storage metrics from Supabase
        const mockUsage = {
          used: 125 * 1024 * 1024, // 125 MB
          total: 1 * 1024 * 1024 * 1024, // 1 GB
          percentage: 12.2
        };

        setStorageInfo(mockUsage);
      } else {
        // Fallback with mock data
        setStorageInfo({
          used: 125 * 1024 * 1024,
          total: 1 * 1024 * 1024 * 1024,
          percentage: 12.2
        });
      }
    } catch (error) {
      console.error('Error fetching storage info:', error);
      // Fallback with mock data
      setStorageInfo({
        used: 125 * 1024 * 1024,
        total: 1 * 1024 * 1024 * 1024,
        percentage: 12.2
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Storage Monitor</h2>
        <Button onClick={fetchStorageInfo} disabled={refreshing}>
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {storageInfo && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <HardDrive className="w-5 h-5 text-blue-600" />
                <span>Storage Usage</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Used</span>
                    <span>{storageInfo.percentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(storageInfo.percentage, 100)}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500 mt-1">
                    <span>{formatBytes(storageInfo.used)}</span>
                    <span>{formatBytes(storageInfo.total)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-green-600" />
                <span>Available Space</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatBytes(storageInfo.total - storageInfo.used)}
              </div>
              <p className="text-gray-600">Remaining storage space</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-orange-600" />
                <span>Total Quota</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {formatBytes(storageInfo.total)}
              </div>
              <p className="text-gray-600">Total allocated storage</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Storage Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none">
            <p className="text-gray-700">
              This dashboard shows your Supabase storage usage. Storage is used for uploaded files
              such as images, documents, and other media assets.
            </p>
            <ul className="text-gray-700 mt-4 space-y-2">
              <li>• Images uploaded for UMKM profiles</li>
              <li>• Documents and files shared on the website</li>
              <li>• Backup files and system data</li>
            </ul>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900">Tips for Managing Storage:</h4>
              <ul className="text-blue-800 mt-2 space-y-1">
                <li>• Optimize image sizes before uploading</li>
                <li>• Remove unused files regularly</li>
                <li>• Use appropriate file formats (WebP for images)</li>
                <li>• Monitor usage to avoid quota limits</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}